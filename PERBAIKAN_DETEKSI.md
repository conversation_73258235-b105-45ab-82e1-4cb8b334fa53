# Perbaikan Deteksi Kendaraan - index.py

## <PERSON><PERSON><PERSON> yang Ditemukan

1. **Confidence threshold terlalu rendah (0.2)**
   - Menyebabkan banyak false positive
   - Banner, papan reklame, dan objek lain terdeteksi sebagai kendaraan

2. **Tidak ada validasi ukuran objek**
   - Objek kecil (banner) bisa terdeteksi sebagai truk
   - Objek dengan proporsi salah tidak difilter

3. **Tidak ada Region of Interest (ROI)**
   - Objek di trotoar ikut terdeteksi
   - Tidak membedakan area jalan dengan area non-jalan

4. **Preprocessing berlebihan**
   - CLAHE dan Gaus<PERSON> blur mengubah karakteristik objek
   - Bisa menyebabkan misklasifikasi

## Perbaikan yang Dilakukan

### 1. Peningkatan Confidence Threshold
```python
# Sebelum
model.conf = 0.2

# Sesudah  
model.conf = 0.5  # Le<PERSON>h ketat untuk mengurangi false positive
```

### 2. Validasi Ukuran Objek
Ditambahkan fungsi `validate_vehicle_size()` yang memvalidasi:
- **Area relatif** terhadap frame
- **Aspect ratio** (perbandingan lebar/tinggi)

**Parameter validasi per jenis kendaraan:**
- **Mobil**: area 0.1%-30%, aspect ratio 1.2-3.0
- **Motor**: area 0.05%-10%, aspect ratio 0.8-2.5  
- **Bus**: area 1%-50%, aspect ratio 2.0-4.0
- **Truk**: area 0.5%-40%, aspect ratio 1.5-3.5
- **Sepeda**: area 0.03%-5%, aspect ratio 0.8-2.0

### 3. Region of Interest (ROI)
Ditambahkan fungsi `is_in_road_area()` yang:
- Membatasi deteksi hanya di area jalan
- Mengabaikan objek di trotoar (15% kiri-kanan frame)
- Mengabaikan objek di langit/bangunan (20% atas frame)

### 4. Preprocessing Minimal
```python
# Sebelum: CLAHE + Gaussian blur (15 baris kode)
# Sesudah: Preprocessing minimal (3 baris kode)
```

### 5. Fitur Debugging
- **Visualisasi ROI**: Garis kuning menunjukkan area deteksi
- **Info ukuran objek**: Menampilkan persentase area objek
- **Kontrol real-time**: 
  - `+` untuk menaikkan confidence
  - `-` untuk menurunkan confidence
  - `r` untuk reset counter

## Cara Menggunakan

1. **Jalankan program**:
   ```bash
   python index.py
   ```

2. **Kontrol keyboard**:
   - `q`: Keluar dari program
   - `r`: Reset counter kendaraan
   - `+` atau `=`: Naikkan confidence threshold
   - `-`: Turunkan confidence threshold

3. **Monitoring**:
   - Garis kuning menunjukkan zona deteksi
   - Label menampilkan jenis kendaraan, confidence, dan ukuran area
   - Info real-time di pojok kiri atas

## Hasil yang Diharapkan

✅ **Mengurangi false positive**:
- Banner tidak lagi terdeteksi sebagai truk
- Objek di trotoar diabaikan

✅ **Meningkatkan akurasi klasifikasi**:
- Mobil tidak salah diklasifikasi sebagai bus
- Validasi ukuran mencegah kesalahan deteksi

✅ **Kontrol yang lebih baik**:
- Dapat menyesuaikan sensitivity secara real-time
- Visualisasi zona deteksi untuk debugging

## Catatan Penting

- **ROI dapat disesuaikan** berdasarkan sudut kamera CCTV
- **Parameter validasi ukuran** dapat di-fine-tune sesuai kondisi lapangan
- **Confidence threshold** dapat diatur secara real-time menggunakan keyboard
