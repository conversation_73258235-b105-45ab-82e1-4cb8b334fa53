import cv2
import torch
import numpy as np

# Ganti 'http://source-url.com/stream' dengan URL sumber video
stream_url = 'http://stream.cctv.malangkota.go.id/WebRTCApp/streams/963661947392775741127849.m3u8'

# Memuat model YOLOv5 dengan force_reload untuk mengatasi cache yang rusak
try:
    print("Loading YOLOv5 model...")
    model = torch.hub.load('ultralytics/yolov5', 'yolov5m')  # Model medium YOLOv5 (yolov5m)
    print("YOLOv5m model loaded successfully!")
except Exception as e:
    print(f"Error loading yolov5m: {e}")
    print("Trying yolov5s as fallback...")
    try:
        model = torch.hub.load('ultralytics/yolov5', 'yolov5s')  # Fallback ke model kecil
        print("YOLOv5s model loaded successfully!")
    except Exception as e2:
        print(f"Error loading yolov5s: {e2}")
        print("Trying local model files...")
        # Coba gunakan file lokal jika ada
        if torch.cuda.is_available():
            device = 'cuda'
        else:
            device = 'cpu'

        # Coba load model lokal
        try:
            model = torch.hub.load('ultralytics/yolov5', 'custom', path='yolov5s.pt')
            print("Local YOLOv5s model loaded successfully!")
        except:
            print("Loading basic YOLOv5s model...")
            model = torch.hub.load('ultralytics/yolov5', 'yolov5s', trust_repo=True)

# Konfigurasi model untuk deteksi yang seimbang
model.conf = 0.3  # Confidence threshold seimbang antara akurasi dan sensitivitas
model.iou = 0.45  # IoU threshold untuk NMS
model.max_det = 1000  # Maksimum deteksi per gambar

# Class IDs untuk semua jenis kendaraan di COCO dataset
VEHICLE_CLASSES = [
    2,   # car
    3,   # motorcycle
    5,   # bus
    7,   # truck
    1,   # bicycle (sepeda juga kendaraan)
]

# Fungsi untuk validasi ukuran objek berdasarkan jenis kendaraan
def validate_vehicle_size(x1, y1, x2, y2, class_id, frame_width, frame_height):
    """Validasi ukuran objek untuk mengurangi false positive"""
    width = x2 - x1
    height = y2 - y1
    area = width * height

    # Persentase area relatif terhadap frame
    relative_area = area / (frame_width * frame_height)

    # Rasio aspek (width/height)
    aspect_ratio = width / height if height > 0 else 0

    # Validasi berdasarkan jenis kendaraan (parameter lebih fleksibel)
    if class_id == 2:  # car
        # Mobil: area 0.0005-0.4, aspect ratio 0.8-4.0 (lebih fleksibel)
        return 0.0005 <= relative_area <= 0.4 and 0.8 <= aspect_ratio <= 4.0
    elif class_id == 3:  # motorcycle
        # Motor: area 0.0002-0.15, aspect ratio 0.5-3.0 (lebih fleksibel)
        return 0.0002 <= relative_area <= 0.15 and 0.5 <= aspect_ratio <= 3.0
    elif class_id == 5:  # bus
        # Bus: area 0.005-0.6, aspect ratio 1.5-5.0 (lebih fleksibel)
        return 0.005 <= relative_area <= 0.6 and 1.5 <= aspect_ratio <= 5.0
    elif class_id == 7:  # truck
        # Truk: area 0.003-0.5, aspect ratio 1.0-4.0 (lebih fleksibel)
        return 0.003 <= relative_area <= 0.5 and 1.0 <= aspect_ratio <= 4.0
    elif class_id == 1:  # bicycle
        # Sepeda: area 0.0001-0.08, aspect ratio 0.5-2.5 (lebih fleksibel)
        return 0.0001 <= relative_area <= 0.08 and 0.5 <= aspect_ratio <= 2.5

    return True  # Default untuk kelas lain

# Fungsi untuk validasi posisi objek (ROI - Region of Interest)
def is_in_road_area(x1, y1, x2, y2, frame_width, frame_height):
    """Validasi apakah objek berada di area jalan (bukan trotoar)"""
    center_x = (x1 + x2) / 2
    center_y = (y1 + y2) / 2

    # Definisi area jalan (lebih luas untuk menangkap lebih banyak kendaraan)
    # Area yang lebih fleksibel untuk menghindari kehilangan deteksi
    road_left = frame_width * 0.1    # 10% dari kiri adalah trotoar (lebih sempit)
    road_right = frame_width * 0.9   # 10% dari kanan adalah trotoar (lebih sempit)
    road_top = frame_height * 0.15   # 15% dari atas adalah langit/bangunan (lebih rendah)

    # Objek harus berada di area jalan
    return road_left <= center_x <= road_right and center_y >= road_top

# Membuka stream
cap = cv2.VideoCapture(stream_url)
cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Buffer lebih kecil untuk real-time
cap.set(cv2.CAP_PROP_FPS, 30)  # Set FPS

if not cap.isOpened():
    print("Gagal membuka stream!")
    exit()

# Mengurangi frame skipping untuk deteksi yang lebih baik
frame_skip = 1  # Proses setiap frame (atau ubah ke 2 jika performa lambat)
frame_count = 0
vehicle_count = 0  # Counter untuk kendaraan yang terdeteksi
while True:
    ret, frame = cap.read()

    if not ret:
        print("Gagal membaca frame!")
        break

    # Simpan frame original untuk display
    original_frame = frame.copy()

    # Resize dengan resolusi yang lebih tinggi untuk deteksi yang lebih baik
    frame = cv2.resize(frame, (1280, 720))  # Resolusi lebih tinggi

    frame_count += 1
    if frame_count % frame_skip != 0:
        continue  # Lewati frame

    # Preprocessing minimal untuk menjaga karakteristik objek
    # Hanya normalisasi ringan jika diperlukan
    # frame = cv2.convertScaleAbs(frame, alpha=1.1, beta=10)  # Sedikit peningkatan kontras

    # Deteksi objek menggunakan YOLOv5
    results = model(frame)  # Hasil deteksi langsung dari model YOLOv5

    # Ambil hasil deteksi
    detections = results.xyxy[0].cpu().numpy()  # Bounding boxes (x1, y1, x2, y2, conf, class)

    # Reset frame untuk display (gunakan original frame yang di-resize)
    display_frame = cv2.resize(original_frame, (1280, 720))

    # Counter untuk frame ini
    current_frame_vehicles = 0
    debug_info = []  # Untuk menyimpan info debugging

    # Loop untuk setiap deteksi
    for detection in detections:
        x1, y1, x2, y2, conf, class_id = detection
        class_id = int(class_id)

        # Filter deteksi kendaraan dengan validasi yang lebih fleksibel
        # Cek confidence threshold yang disesuaikan
        confidence_ok = conf > 0.3

        # Cek apakah kelas kendaraan
        is_vehicle = class_id in VEHICLE_CLASSES

        # Validasi ukuran (opsional untuk kendaraan kecil/jauh)
        size_ok = validate_vehicle_size(x1, y1, x2, y2, class_id, 1280, 720)

        # Validasi ROI (opsional untuk kendaraan di pinggir)
        roi_ok = is_in_road_area(x1, y1, x2, y2, 1280, 720)

        # Logika deteksi yang lebih fleksibel:
        # - Confidence tinggi (>0.5): langsung terima tanpa validasi lain
        # - Confidence sedang (0.3-0.5): perlu validasi ukuran ATAU ROI
        # - Confidence rendah (<0.3): tolak
        if confidence_ok and is_vehicle:
            if conf > 0.5:  # Confidence tinggi, langsung terima
                should_detect = True
            elif conf > 0.4:  # Confidence sedang-tinggi, perlu salah satu validasi
                should_detect = size_ok or roi_ok
            else:  # Confidence sedang-rendah, perlu kedua validasi
                should_detect = size_ok and roi_ok

        else:
            should_detect = False

        # Debug info untuk semua deteksi kendaraan (terdeteksi atau tidak)
        if is_vehicle:
            width = x2 - x1
            height = y2 - y1
            area_percent = (width * height) / (1280 * 720) * 100
            aspect_ratio = width / height if height > 0 else 0
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2

            vehicle_names = {2: "Mobil", 3: "Motor", 5: "Bus", 7: "Truk", 1: "Sepeda"}
            vehicle_name = vehicle_names.get(class_id, "Kendaraan")

            debug_info.append({
                'type': vehicle_name,
                'conf': conf,
                'area': area_percent,
                'ratio': aspect_ratio,
                'pos': (center_x, center_y),
                'size_ok': size_ok,
                'roi_ok': roi_ok,
                'detected': should_detect
            })

        if should_detect:
            current_frame_vehicles += 1

            # Tentukan warna berdasarkan jenis kendaraan
            if class_id == 2:  # car
                color = (0, 255, 0)  # Hijau
                vehicle_type = "Mobil"
            elif class_id == 3:  # motorcycle
                color = (0, 0, 255)  # Merah
                vehicle_type = "Motor"
            elif class_id == 5:  # bus
                color = (255, 0, 0)  # Biru
                vehicle_type = "Bus"
            elif class_id == 7:  # truck
                color = (0, 255, 255)  # Kuning
                vehicle_type = "Truk"
            elif class_id == 1:  # bicycle
                color = (255, 0, 255)  # Magenta
                vehicle_type = "Sepeda"
            else:
                color = (128, 128, 128)  # Abu-abu
                vehicle_type = "Kendaraan"

            # Gambar bounding box dengan ketebalan yang lebih tebal
            cv2.rectangle(display_frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 3)

            # Label dengan informasi lebih detail termasuk ukuran untuk debugging
            width = x2 - x1
            height = y2 - y1
            area_percent = (width * height) / (1280 * 720) * 100
            label = f"{vehicle_type}: {conf:.2f} ({area_percent:.1f}%)"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

            # Background untuk label
            cv2.rectangle(display_frame, (int(x1), int(y1)-label_size[1]-10),
                         (int(x1)+label_size[0], int(y1)), color, -1)

            # Text label
            cv2.putText(display_frame, label, (int(x1), int(y1)-5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

    # Update total vehicle count
    if current_frame_vehicles > 0:
        vehicle_count += current_frame_vehicles

    # Gambar ROI (Region of Interest) untuk debugging
    road_left = int(1280 * 0.1)
    road_right = int(1280 * 0.9)
    road_top = int(720 * 0.15)
    cv2.line(display_frame, (road_left, 0), (road_left, 720), (255, 255, 0), 2)  # Garis kiri
    cv2.line(display_frame, (road_right, 0), (road_right, 720), (255, 255, 0), 2)  # Garis kanan
    cv2.line(display_frame, (0, road_top), (1280, road_top), (255, 255, 0), 2)  # Garis atas

    # Tambahkan informasi statistik di frame
    info_text = f"Kendaraan terdeteksi: {current_frame_vehicles} | Total: {vehicle_count}"
    cv2.putText(display_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(display_frame, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 1)

    # Tambahkan informasi frame dan confidence threshold
    frame_info = f"Frame: {frame_count} | Conf: {model.conf}"
    cv2.putText(display_frame, frame_info, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(display_frame, frame_info, (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)

    # Tambahkan informasi ROI
    roi_info = "ROI: Area kuning = zona deteksi kendaraan"
    cv2.putText(display_frame, roi_info, (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

    # Tambahkan debug info untuk kendaraan yang tidak terdeteksi
    debug_y = 150
    for info in debug_info:
        if not info['detected']:  # Hanya tampilkan yang tidak terdeteksi
            debug_text = f"MISSED: {info['type']} conf:{info['conf']:.2f} area:{info['area']:.1f}% ratio:{info['ratio']:.1f}"
            cv2.putText(display_frame, debug_text, (10, debug_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            debug_y += 20
            if debug_y > 300:  # Batasi jumlah debug info
                break

    # Tampilkan frame dengan deteksi kendaraan
    cv2.imshow('Live Streaming - Deteksi Kendaraan (Enhanced)', display_frame)

    # Tekan 'q' untuk keluar, 'r' untuk reset counter, '+'/'-' untuk adjust confidence
    key = cv2.waitKey(1) & 0xFF
    if key == ord('q'):
        break
    elif key == ord('r'):
        vehicle_count = 0
        print("Counter kendaraan direset!")
    elif key == ord('+') or key == ord('='):
        model.conf = min(0.9, model.conf + 0.05)
        print(f"Confidence threshold dinaikkan ke: {model.conf:.2f}")
    elif key == ord('-'):
        model.conf = max(0.1, model.conf - 0.05)
        print(f"Confidence threshold diturunkan ke: {model.conf:.2f}")
    elif key == ord('d'):
        # Toggle debug mode - print semua deteksi ke console
        if debug_info:
            print(f"\n=== DEBUG FRAME {frame_count} ===")
            for info in debug_info:
                status = "DETECTED" if info['detected'] else "MISSED"
                print(f"{status}: {info['type']} | Conf: {info['conf']:.2f} | Area: {info['area']:.1f}% | Ratio: {info['ratio']:.1f} | Pos: ({info['pos'][0]:.0f},{info['pos'][1]:.0f}) | Size_OK: {info['size_ok']} | ROI_OK: {info['roi_ok']}")
            print("=" * 50)

    # Print informasi setiap 100 frame
    if frame_count % 100 == 0:
        print(f"Frame {frame_count}: {current_frame_vehicles} kendaraan terdeteksi, Total: {vehicle_count}")

# Bersihkan sumber daya
print(f"\nProgram selesai. Total kendaraan yang terdeteksi: {vehicle_count}")
cap.release()
cv2.destroyAllWindows()
